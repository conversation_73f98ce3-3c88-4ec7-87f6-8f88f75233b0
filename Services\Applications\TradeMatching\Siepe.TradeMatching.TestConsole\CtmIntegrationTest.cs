
using Siepe.TradeMatching.Entities.CTM;
using Siepe.TradeMatching.Services.CTM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using Siepe.TradeMatching.Services.CTM.Extensions;
using Siepe.TradeMatching.Services.CTM.ResponseMessageProcessors;
using Microsoft.Extensions.DependencyInjection;


namespace Siepe.TradeMatching.Tests
{
    public class CtmIntegrationTest
    {
        private readonly CtmTestProvider _testProvider;
        private readonly CtmClient _client;
        private readonly CtmConfiguration _config;
        private readonly IMessageContainerService _messageContainerService;
        private readonly IMessageBuilder _messageBuilder;
        private readonly IServiceProvider _serviceProvider;
        private readonly EventProcessor _eventProcessor;

        public CtmIntegrationTest()
        {
            _config = new CtmConfiguration()
            {
                ClientBIC = "NPALUS42", 
                ClientIdentifier = "NPALUS42",
                MessageProcessFrequency = 2,
                Password = "Skyview14!",
                Username = "cmacn133",
                //ClientBIC = "SIEPEHUB",
                //ClientIdentifier = "SIEPEHUB",
                //MessageProcessFrequency = 2,
                //Password = "!t@@5440Harvest",
                //Username = "cmacs345",
                WebEndPoint = "https://cmict.omgeo.net/cmidirect/"
            };
            
            //_messageContainerService = new MessageContainerService(_config);
            //_messageBuilder = new CtmMessageBuilder(_config);
            //var clientFactoryMock = Substitute.For<IHttpClientFactory>();
            var httpClient = new HttpClient
            {
                BaseAddress = new Uri(_config.WebEndPoint)
            };
            //clientFactoryMock.CreateClient("CtmClient").Returns(httpClient);
            //_client = new CtmClient(clientFactoryMock,_messageContainerService,_config);
            //_testProvider = new CtmTestProvider();
            
            // Get the service provider from our test helper
            //_serviceProvider = TestServiceHelper.GetServiceProvider();
            
            // Get the EventProcessor from DI container
            // Note: EventProcessor is registered as IMessageProcessor, so we need to get all and find the EventProcessor
            //var messageProcessors = _serviceProvider.GetServices<IMessageProcessor>();
            //_eventProcessor = messageProcessors.OfType<EventProcessor>().FirstOrDefault();
            
            
            //if (_eventProcessor == null)
            //{
            //    throw new InvalidOperationException("EventProcessor not found in DI container");
            //}
        }

        
        public void TestMatchRequest()
        {
            var allocationStatus = _testProvider.GetAllocationStatus();
            var instInfo = _testProvider.GetInstInfo();
            var allocation = _testProvider.GetMatchTestAllocation();

            var tradeMessage = _messageBuilder.BuildTradeDetailMessage(allocation, allocationStatus, instInfo);
            tradeMessage.TradeDetail.TradeDetailBody.ShowHiddenFieldsIndicator = new ShowHiddenFieldsIndicator() { Value = "Y" };
            _client.SendMessage(tradeMessage).Wait();

            var messages = new List<CTM_Message>();
            var timeout = TimeSpan.FromMinutes(15);
            var startTime = DateTime.UtcNow;

            while (true)
            {
                Thread.Sleep(5000);
                messages = _client.GetOutboundMessages().Result;

                if (messages.Count > 0)
                {
                    Console.WriteLine($"Received {messages.Count} messages");
                    
                    // Process each message
                    foreach (var message in messages)
                    {
                        Console.WriteLine($"Processing message type: {message.MessageType()}");
                        
                        // Process EventNotification messages through EventProcessor
                        if (message.MessageType() == "EventNotification")
                        {
                            try
                            {
                                _eventProcessor.Process(message);
                                Console.WriteLine("EventProcessor successfully processed EventNotification");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"EventProcessor error: {ex.Message}");
                            }
                        }
                    }
                    
                    // Check if we have valid messages to exit the loop
                    //var validMessages = messages.Where(m => m.MessageType().Equals("Valid")).ToList();
                    //if (validMessages.Count > 0)
                    //{
                    //    break;
                    //}
                }

                if (DateTime.UtcNow - startTime > timeout)
                {
                    throw new TimeoutException("No messages received within the timeout period of 15 minutes.");
                }
            }

            var validMessages = messages.Where(m => m.MessageType().Equals("Valid")).ToList();
            var invalidMessages = messages.Where(m => m.MessageType().Equals("Invalid")).ToList();

         
        }

        public void TestMisMatchRequest()
        {
            var allocationStatus = _testProvider.GetAllocationStatus();
            var instInfo = _testProvider.GetInstInfo();
            var allocation = _testProvider.GetMisMatchTestAllocation();

            var tradeMessage = _messageBuilder.BuildTradeDetailMessage(allocation, allocationStatus, instInfo);
            tradeMessage.TradeDetail.TradeDetailBody.ShowHiddenFieldsIndicator = new ShowHiddenFieldsIndicator() { Value = "Y" };
            _client.SendMessage(tradeMessage).Wait();

            var messages = new List<CTM_Message>();
            var timeout = TimeSpan.FromMinutes(15);
            var startTime = DateTime.UtcNow;

            while (true)
            {
                Thread.Sleep(5000);
                messages = _client.GetOutboundMessages().Result;

                if (messages.Count > 0)
                {
                    break;
                }

                if (DateTime.UtcNow - startTime > timeout)
                {
                    throw new TimeoutException("No messages received within the timeout period of 15 minutes.");
                }
            }

            var misMatchMessages = messages.Where(m => m.MessageType().Equals("Invalid")).ToList();
        }
    }
}