
using Siepe.TradeMatching.Entities.CTM;
using Siepe.TradeMatching.Services.CTM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using Siepe.TradeMatching.Services.CTM.Extensions;
using Siepe.TradeMatching.Services.CTM.ResponseMessageProcessors;
using Microsoft.Extensions.DependencyInjection;


namespace Siepe.TradeMatching.Tests
{
    public class CtmIntegrationTest
    {
        private readonly CtmTestProvider _testProvider;
        private readonly IClient _client;
        private readonly CtmConfiguration _config;
        private readonly IMessageContainerService _messageContainerService;
        private readonly IMessageBuilder _messageBuilder;
        private readonly IServiceProvider _serviceProvider;
        private readonly EventProcessor _eventProcessor;

        public CtmIntegrationTest()
        {
            // Get the service provider from our test helper
            _serviceProvider = TestServiceHelper.GetServiceProvider();

            // Get instances from DI container
            try
            {
                _messageContainerService = _serviceProvider.GetRequiredService<IMessageContainerService>();
                _messageBuilder = _serviceProvider.GetRequiredService<IMessageBuilder>();
                _client = _serviceProvider.GetRequiredService<IClient>();

                // Get the EventProcessor from DI container
                // Note: EventProcessor is registered as IMessageProcessor, so we need to get all and find the EventProcessor
                var messageProcessors = _serviceProvider.GetServices<IMessageProcessor>();
                _eventProcessor = messageProcessors.OfType<EventProcessor>().FirstOrDefault();

                if (_eventProcessor == null)
                {
                    throw new InvalidOperationException("EventProcessor not found in DI container");
                }

                // Create test provider (this is not registered in DI, so we create it directly)
                _testProvider = new CtmTestProvider();

                // Get configuration from DI container (this will be the one configured in TestServiceHelper)
                var configOptions = _serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<CtmConfiguration>>();
                _config = configOptions.Value;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to initialize CtmIntegrationTest from DI container: {ex.Message}", ex);
            }
        }

        
        public void TestMatchRequest()
        {
            var allocationStatus = _testProvider.GetAllocationStatus();
            var instInfo = _testProvider.GetInstInfo();
            var allocation = _testProvider.GetMatchTestAllocation();

            var tradeMessage = _messageBuilder.BuildTradeDetailMessage(allocation, allocationStatus, instInfo);
            tradeMessage.TradeDetail.TradeDetailBody.ShowHiddenFieldsIndicator = new ShowHiddenFieldsIndicator() { Value = "Y" };
            _client.SendMessage(tradeMessage).Wait();

            var messages = new List<CTM_Message>();
            var timeout = TimeSpan.FromMinutes(15);
            var startTime = DateTime.UtcNow;

            while (true)
            {
                Thread.Sleep(5000);
                messages = _client.GetOutboundMessages().Result;

                if (messages.Count > 0)
                {
                    Console.WriteLine($"Received {messages.Count} messages");
                    
                    // Process each message
                    foreach (var message in messages)
                    {
                        Console.WriteLine($"Processing message type: {message.MessageType()}");
                        
                        // Process EventNotification messages through EventProcessor
                        if (message.MessageType() == "EventNotification")
                        {
                            try
                            {
                                _eventProcessor.Process(message);
                                Console.WriteLine("EventProcessor successfully processed EventNotification");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"EventProcessor error: {ex.Message}");
                            }
                        }
                    }
                    
                    // Check if we have valid messages to exit the loop
                    //var validMessages = messages.Where(m => m.MessageType().Equals("Valid")).ToList();
                    //if (validMessages.Count > 0)
                    //{
                    //    break;
                    //}
                }

                if (DateTime.UtcNow - startTime > timeout)
                {
                    throw new TimeoutException("No messages received within the timeout period of 15 minutes.");
                }
            }

            var validMessages = messages.Where(m => m.MessageType().Equals("Valid")).ToList();
            var invalidMessages = messages.Where(m => m.MessageType().Equals("Invalid")).ToList();

         
        }

        public void TestMisMatchRequest()
        {
            var allocationStatus = _testProvider.GetAllocationStatus();
            var instInfo = _testProvider.GetInstInfo();
            var allocation = _testProvider.GetMisMatchTestAllocation();

            var tradeMessage = _messageBuilder.BuildTradeDetailMessage(allocation, allocationStatus, instInfo);
            tradeMessage.TradeDetail.TradeDetailBody.ShowHiddenFieldsIndicator = new ShowHiddenFieldsIndicator() { Value = "Y" };
            _client.SendMessage(tradeMessage).Wait();

            var messages = new List<CTM_Message>();
            var timeout = TimeSpan.FromMinutes(15);
            var startTime = DateTime.UtcNow;

            while (true)
            {
                Thread.Sleep(5000);
                messages = _client.GetOutboundMessages().Result;

                if (messages.Count > 0)
                {
                    break;
                }

                if (DateTime.UtcNow - startTime > timeout)
                {
                    throw new TimeoutException("No messages received within the timeout period of 15 minutes.");
                }
            }

            var misMatchMessages = messages.Where(m => m.MessageType().Equals("Invalid")).ToList();
        }

        /// <summary>
        /// Validates that all required dependencies can be resolved from the DI container
        /// </summary>
        /// <returns>List of any problems encountered when resolving dependencies</returns>
        public List<string> ValidateDependencies()
        {
            var problems = new List<string>();

            try
            {
                var serviceProvider = TestServiceHelper.GetServiceProvider();

                // Test IMessageContainerService
                try
                {
                    var messageContainerService = serviceProvider.GetRequiredService<IMessageContainerService>();
                    if (messageContainerService == null)
                        problems.Add("IMessageContainerService resolved to null");
                }
                catch (Exception ex)
                {
                    problems.Add($"Failed to resolve IMessageContainerService: {ex.Message}");
                }

                // Test IMessageBuilder
                try
                {
                    var messageBuilder = serviceProvider.GetRequiredService<IMessageBuilder>();
                    if (messageBuilder == null)
                        problems.Add("IMessageBuilder resolved to null");
                }
                catch (Exception ex)
                {
                    problems.Add($"Failed to resolve IMessageBuilder: {ex.Message}");
                }

                // Test IClient
                try
                {
                    var client = serviceProvider.GetRequiredService<IClient>();
                    if (client == null)
                        problems.Add("IClient resolved to null");
                }
                catch (Exception ex)
                {
                    problems.Add($"Failed to resolve IClient: {ex.Message}");
                }

                // Test EventProcessor
                try
                {
                    var messageProcessors = serviceProvider.GetServices<IMessageProcessor>();
                    var eventProcessor = messageProcessors.OfType<EventProcessor>().FirstOrDefault();
                    if (eventProcessor == null)
                        problems.Add("EventProcessor not found among registered IMessageProcessor implementations");
                }
                catch (Exception ex)
                {
                    problems.Add($"Failed to resolve EventProcessor: {ex.Message}");
                }

                // Test CtmConfiguration
                try
                {
                    var configOptions = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<CtmConfiguration>>();
                    if (configOptions?.Value == null)
                        problems.Add("CtmConfiguration resolved to null");
                }
                catch (Exception ex)
                {
                    problems.Add($"Failed to resolve CtmConfiguration: {ex.Message}");
                }

            }
            catch (Exception ex)
            {
                problems.Add($"Failed to get service provider: {ex.Message}");
            }

            return problems;
        }

        /// <summary>
        /// Simple test to verify that the DI container setup is working correctly
        /// </summary>
        public void TestDependencyInjection()
        {
            Console.WriteLine("Testing Dependency Injection setup...");

            var problems = ValidateDependencies();

            if (problems.Count == 0)
            {
                Console.WriteLine("✓ All dependencies resolved successfully from DI container!");
                Console.WriteLine($"✓ IMessageContainerService: {_messageContainerService.GetType().Name}");
                Console.WriteLine($"✓ IMessageBuilder: {_messageBuilder.GetType().Name}");
                Console.WriteLine($"✓ IClient: {_client.GetType().Name}");
                Console.WriteLine($"✓ EventProcessor: {_eventProcessor.GetType().Name}");
                Console.WriteLine($"✓ CtmTestProvider: {_testProvider.GetType().Name}");
                Console.WriteLine($"✓ CtmConfiguration: {_config.GetType().Name}");
            }
            else
            {
                Console.WriteLine("❌ Problems found with dependency resolution:");
                foreach (var problem in problems)
                {
                    Console.WriteLine($"  - {problem}");
                }
            }
        }
    }
}