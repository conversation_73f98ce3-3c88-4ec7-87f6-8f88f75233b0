using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Serilog;
using Siepe.Infrastructure.Logging;
using Siepe.Infrastructure.Logging.Extensions;
using Siepe.Infrastructure.Logging.QueryLogging.Extensions;
using Siepe.Infrastructure.PubSub.Common;
using Siepe.Infrastructure.PubSub.RabbitMq;
using Siepe.Infrastructure.Utilities.Services;
using Siepe.NetCore.Infrastructure.Extensions;
using Siepe.Shared.DBUtility.Common;
using Siepe.Shared.DBUtility.v1;
using Siepe.TradeMatching.DataAccess;
using Siepe.TradeMatching.DataAccess.CTM;
using Siepe.TradeMatching.Entities.CTM;
using Siepe.TradeMatching.Services;
using Siepe.TradeMatching.Services.CTM;
using Siepe.TradeMatching.Services.CTM.EventHandlers;
using Siepe.TradeMatching.Services.CTM.MessageHandlers;
using Siepe.TradeMatching.Services.CTM.ResponseMessageProcessors;
using System;
using System.Net;
using System.Net.Http;

namespace Siepe.TradeMatching.Tests
{
    public static class TestServiceHelper
    {
        private static IServiceProvider _serviceProvider;
        private static readonly object _lock = new object();

        public static IServiceProvider GetServiceProvider()
        {
            if (_serviceProvider == null)
            {
                lock (_lock)
                {
                    if (_serviceProvider == null)
                    {
                        _serviceProvider = BuildServiceProvider();
                    }
                }
            }
            return _serviceProvider;
        }

        private static IServiceProvider BuildServiceProvider()
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton<IConfiguration>(configuration);
            services.AddOptions();
            services.Configure<CtmConfiguration>(configuration.GetSection("TradeMatchSettings"));

            // Configure logging
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console()
                .CreateLogger();
            services.AddLogging(builder => builder.AddSerilog());

            // Configure HTTP client with cookie support
            services.AddHttpClient("CtmClient")
                .ConfigureHttpClient((serviceProvider, client) =>
                {
                    var settings = serviceProvider.GetRequiredService<IOptions<CtmConfiguration>>().Value;
                    client.BaseAddress = new Uri(settings.WebEndPoint);
                })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                return new HttpClientHandler
                {
                    UseCookies = true,
                    CookieContainer = new CookieContainer()
                };
            });

            // Add all services
            services.AddTransient<IDbAccess, SqlDbAccess>(s => 
                new SqlDbAccess("CoreConnectionString", 
                    new ConfigConnectionStringProvider(configuration), 
                    new NullQueryLogger(), 
                    s.GetRequiredService<ILogger<SqlDbAccess>>()));
            
            services.AddTransient<IClient, CtmClient>(c =>
            {
                var config = c.GetRequiredService<IOptions<CtmConfiguration>>();
                return new CtmClient(c.GetRequiredService<IHttpClientFactory>(), 
                    c.GetRequiredService<IMessageContainerService>(), config.Value);
            });
            
            services.AddTransient<ICtmService, CtmService>();
            services.AddTransient<IAllocationProvider, CtmAllocationProvider>();
            services.AddTransient<Siepe.TradeMatching.DataAccess.IConfigurationProvider, CtmConfigurationProvider>();
            services.AddTransient<IStatusProvider, CtmStatusProvider>();
            services.AddTransient<IInstrumentProvider, CtmInstrumentProvider>();
            services.AddTransient<IMessageContainerService, MessageContainerService>(c =>
            {
                var config = c.GetRequiredService<IOptions<CtmConfiguration>>();
                return new MessageContainerService(config.Value);
            });
            services.AddTransient<ITradeMatchService, CtmTradeMatchService>();

            // Message handlers
            services.AddTransient<IMessageHandler<string>, CancelTradeHandler>();
            services.AddTransient<IMessageHandler<string>, ForceMatchTradeHandler>();
            services.AddTransient<IMessageHandler<string>, RejectTradeHandler>();
            services.AddTransient<IMessageHandler<string>, SubmitSingleTradeHandler>();
            services.AddTransient<IMessageHandler<string>, SubmitTradeHandler>();
            services.AddTransient<IMessageBuilder, CtmMessageBuilder>(c =>
            {
                var config = c.GetRequiredService<IOptions<CtmConfiguration>>();
                return new CtmMessageBuilder(config.Value);
            });

            // Response message processors
            services.AddTransient<IResponseMessageProcessor, ResponseMessageProcessor>();
            services.AddTransient<IMessageProcessor, EventProcessor>();
            services.AddTransient<IMessageProcessor, InfoResponseMessageHandler>();
            services.AddTransient<IMessageProcessor, HistoryResponseMessageHandler>();
            services.AddTransient<IMessageProcessor, InfoSettlementResponseMessageHandler>();
            services.AddTransient<IMessageProcessor, InvalidProcessor>();
            services.AddTransient<IMessageProcessor, ValidProcessor>();
            services.AddTransient<IEventHandlerProcessor, EventHandlerProcessor>();

            // Trade level event handlers
            services.AddTransient<ITLEventHandlerProcessor, TLEventHandlerProcessor>();
            services.AddTransient<ITLEventHandler, CancelEventHandler>();
            services.AddTransient<ITLEventHandler, CompleteEventHandler>();
            services.AddTransient<ITLEventHandler, Siepe.TradeMatching.Services.CTM.EventHandlers.ErrorEventHandler>();
            services.AddTransient<ITLEventHandler, InfoEventHandler>();
            services.AddTransient<ITLEventHandler, CancelMatchAgreed>();
            services.AddTransient<ITLEventHandler, CounterpartyMatchAgreeCancelRequest>();
            services.AddTransient<ITLEventHandler, CounterpartyMatchAgreeCancelRejected>();
            services.AddTransient<ITLEventHandler, MatchAgreeCancelRequest>();
            services.AddTransient<ITLEventHandler, MatchAgreedHandler>();
            services.AddTransient<ITLEventHandler, MatchAgreeRejected>();
            services.AddTransient<ITLEventHandler, MatchedEventHandler>();
            services.AddTransient<ITLEventHandler, MismatchedEventHandler>();
            services.AddTransient<ITLEventHandler, RejectionEventHandler>();
            services.AddTransient<ITLEventHandler, UnmatchedEventHandler>();
            services.AddTransient<ITLEventHandler, WarnEventHandler>();

            // Trade detail event handlers
            services.AddTransient<IEventHandler, CancelEventHandler>();
            services.AddTransient<IEventHandler, Siepe.TradeMatching.Services.CTM.EventHandlers.ErrorEventHandler>();
            services.AddTransient<IEventHandler, ForceMatchEventHandler>();
            services.AddTransient<IEventHandler, InfoEventHandler>();
            services.AddTransient<IEventHandler, MatchedEventHandler>();
            services.AddTransient<IEventHandler, MismatchedEventHandler>();
            services.AddTransient<IEventHandler, RejectionEventHandler>();
            services.AddTransient<IEventHandler, UnmatchedEventHandler>();
            services.AddTransient<IEventHandler, WarnEventHandler>();

            return services.BuildServiceProvider();
        }

        public static T GetService<T>()
        {
            return GetServiceProvider().GetRequiredService<T>();
        }
    }
}